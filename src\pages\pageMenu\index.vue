<!--
 * @Description: 城市运行管理服务系统主页
 * @Version: 2.0
 * @Autor: wjb
 * @Date: 2025-06-19 08:32:42
 * @LastEditors: wjb
 * @LastEditTime: 2025-06-30 09:50:59
-->
<template>
  <div class="choosePage" id="choosePage">
    <div class="name">
      {{ city + '城市运行管理服务系统' }}
    </div>
    <div class="tops">
      <div
        class="topItem"
        v-for="(item, i) in topArr"
        :key="i"
        @click="pageJump(item)"
        :style="{ background: 'url(' + item.back + ')' }"
      ></div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-container">
      <img class="roud_bkg" src="@/assets/home/<USER>" alt="" />
      <!-- 横向滚动的数字 -->
      <svg
        width="7680"
        height="2160"
        xmlns="http://www.w3.org/2000/svg"
        xmlns:xlink="http://www.w3.org/1999/xlink"
        style="position: absolute"
      >
        <g>
          <defs>
            <path
              id="path1_1"
              d="m34.99887,1355.00008c0,0 835.00024,-260.00007 1340.00038,-80.00002c505.00014,180.00005 845.00024,200.00006 865.00024,195.00006c20.00001,-5 235.00007,15 355.0001,-50.00001"
              opacity="0"
            />
          </defs>

          <use xlink:href="#path1_1" fill="none" stroke="red" />

          <text font-family="Verdana" font-size="45" fill="#8dd1f8" stroke-width="1" stroke="#8dd1f8" opacity="0.3">
            <textPath xlink:href="#path1_1">
              0101010101010101010101010101010101010101010101010101
              <animate
                attributeName="startOffset"
                from="1200"
                to="-1400"
                begin="0s"
                dur="5s"
                repeatCount="indefinite"
              />
            </textPath>
          </text>
        </g>
        <g>
          <defs>
            <path
              id="path1_2"
              d="m42.49958,1501.24724c0,0 437.49709,-62.49958 484.99678,-82.49945c47.49968,-19.99987 227.49849,-24.99983 277.49816,2.49998c49.99967,27.49982 354.99764,62.49958 679.99548,69.99954c324.99784,7.49995 922.49387,12.49992 912.49394,-32.49978"
              opacity="0"
            />
          </defs>

          <use xlink:href="#path1_2" fill="none" stroke="red" />

          <text font-family="Verdana" font-size="45" fill="#8dd1f8" stroke-width="1" stroke="#8dd1f8" opacity="0.3">
            <textPath xlink:href="#path1_2">
              0101010101010101010101010101010101010101010101010101
              <animate
                attributeName="startOffset"
                from="2500"
                to="-1400"
                begin="0s"
                dur="6s"
                repeatCount="indefinite"
              />
            </textPath>
          </text>
        </g>
        <g>
          <defs>
            <!-- <path id="path1_3"
              d="m7677.44888,1408.74785c0,0 -634.99578,-257.49829 -1247.49172,-104.9993c-612.49593,152.49899 -1524.98987,364.99758 -1674.98888,74.9995"
              opacity="0" /> -->
            <path
              id="path1_3"
              d="m4782.47948,1388.74869c0,0 297.49872,277.49881 1339.99425,-37.49984c1042.49553,-314.99865 1552.49334,29.99987 1552.49309,28.74518"
              opacity="0"
            />
          </defs>

          <use xlink:href="#path1_3" fill="none" stroke="red" />

          <text font-family="Verdana" font-size="45" fill="#8dd1f8" stroke-width="1" stroke="#8dd1f8" opacity="0.3">
            <textPath xlink:href="#path1_3">
              0101010101010101010101010101010101010101010101010101
              <animate
                attributeName="startOffset"
                from="-1400"
                to="2500"
                begin="0s"
                dur="5s"
                repeatCount="indefinite"
              />
            </textPath>
          </text>
        </g>
        <g>
          <defs>
            <path
              id="path1_4"
              d="m4507.48066,1596.2478c-42.49982,-29.99987 352.49849,147.49937 907.49611,54.99976c554.99762,-92.4996 817.49649,-112.49952 1372.49411,-42.49982c554.99762,69.9997 887.49619,-42.49982 887.49619,-42.49982"
              opacity="0"
            />
          </defs>

          <use xlink:href="#path1_4" fill="none" stroke="red" />

          <text font-family="Verdana" font-size="45" fill="#8dd1f8" stroke-width="1" stroke="#8dd1f8" opacity="0.3">
            <textPath xlink:href="#path1_4">
              0101010101010101010101010101010101010101010101010101
              <animate
                attributeName="startOffset"
                from="-1400"
                to="2500"
                begin="0s"
                dur="6s"
                repeatCount="indefinite"
              />
            </textPath>
          </text>
        </g>
      </svg>
      <canvas
        id="canvas"
        ref="canvas"
        style="
          z-index: 0;
          top: 50%;
          left: 30%;
          width: 1500px;
          height: 600px;
          position: absolute;
          transform: rotate(180deg);
          border-radius: 50%;
        "
      ></canvas>
      <div class="car_box">
        <el-carousel
          :autoplay="false"
          indicator-position="none"
          arrow="never"
          type="card"
          height="849px"
          :loop="true"
          @change="changeCard"
          ref="carousel"
        >
          <el-carousel-item v-for="(item, index) in bannerList" :key="index">
            <img :src="item.url" alt="" />
          </el-carousel-item>
        </el-carousel>
      </div>
      <div class="wlgz-con">
        <div class="city-swipper-item" v-for="(item, index) in curvedMenuItems" :key="index" @click="pageJump(item)">
          <img :src="item.icon" alt="" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getUrl, getSzcjToken, getUserArea, loginByThirdComplete } from '@/api/indexApi'
import mainObj from '@/utils/PocMain.js'
import { Encrypt } from '@/utils/Encrypt'
// 导入本地走马灯组件
import Carousel from '@/components/carousel/src/main'
import CarouselItem from '@/components/carousel/src/item'

export default {
  name: 'index',
  components: {
    'el-carousel': Carousel,
    'el-carousel-item': CarouselItem,
  },
  data() {
    return {
      city: localStorage.getItem('adminCity') || '',
      topArr: [
        // {
        //   back: require('@/assets/login/login_csyxgl.png'),
        //   name: '首页',
        //   key: 'csyxgl',
        // },
        {
          back: require('@/assets/login/login_admin.png'),
          name: '后台管理',
        },
      ],
      bannerList: [
        {
          url: require('@/assets/home/<USER>'),
          name: '左侧',
        },
        {
          url: require('@/assets/home/<USER>'),
          name: '右侧',
        },
        {
          url: require('@/assets/home/<USER>'),
          name: '左侧',
        },
      ],
      // 贝塞尔曲线排列的7个主要功能图标
      i: 0,
      commInterval: null,
      DIS: null,
      curvedMenuItems: [
        {
          icon: require('@/assets/home/<USER>'),
          name: '行政执法一张图',
          key: 'home',
        },
        {
          icon: require('@/assets/home/<USER>'),
          name: '执法态势一张图',
          key: 'zfts',
        },
        {
          icon: require('@/assets/home/<USER>'),
          name: '指挥调度一张图',
          key: 'zhdd',
        },
        {
          icon: require('@/assets/home/<USER>'),
          name: '三色预警一张图',
          key: 'ssyj',
        },
        {
          icon: require('@/assets/home/<USER>'),
          name: '行政执法综合评价一张图',
          key: 'xxzf_zhpj',
        },
        {
          icon: require('@/assets/home/<USER>'),
          name: '案件回访',
          key: 'ajhf',
        },
        {
          icon: require('@/assets/home/<USER>'),
          name: '县级中心',
          key: 'xjzx',
        },
      ],
      // 左侧菜单项目
      leftMenuItems: [
        {
          icon: require('@/assets/home/<USER>'),
          name: '行政执法一张图',
          key: 'home',
        },
        {
          icon: require('@/assets/home/<USER>'),
          name: '执法态势一张图',
          key: 'zfts',
        },
        {
          icon: require('@/assets/home/<USER>'),
          name: '指挥调度一张图',
          key: 'zhdd',
        },
        {
          icon: require('@/assets/home/<USER>'),
          name: '三色预警一张图',
          key: 'ssyj',
        },
        {
          icon: require('@/assets/home/<USER>'),
          name: '行政执法综合评价一张图',
          key: 'xxzf_zhpj',
        },
        {
          icon: require('@/assets/home/<USER>'),
          name: '案件回访',
          key: 'ajhf',
        },
        {
          icon: require('@/assets/home/<USER>'),
          name: '县级中心',
          key: 'xjzx',
        },
      ],
      // 右侧菜单项目
      rightMenuItems: [
        {
          icon: require('@/assets/home/<USER>'),
          name: '城市运行管理服务首页',
          key: 'csyxgl',
        },
        {
          icon: require('@/assets/home/<USER>'),
          name: '城市管理一张图',
          key: 'csgl',
        },
        {
          icon: require('@/assets/home/<USER>'),
          name: '物联网设备一张图',
          key: 'wlwsb',
        },
        {
          icon: require('@/assets/home/<USER>'),
          name: '垃圾分类一张图',
          key: 'ljfl',
        },
        {
          icon: require('@/assets/home/<USER>'),
          name: '公众服务一张图',
          key: 'gzfw',
        },
        {
          icon: require('@/assets/home/<USER>'),
          name: '综合评价一张图',
          key: 'zhpj',
        },
        {
          icon: require('@/assets/home/<USER>'),
          name: '运行监测一张图',
          key: 'yxjc',
        },
        {
          icon: require('@/assets/home/<USER>'),
          name: '犬类管理一张图',
          key: 'dog',
        },
        {
          icon: require('@/assets/home/<USER>'),
          name: '站前执法一张图',
          key: 'zqyzt',
        },
        {
          icon: require('@/assets/home/<USER>'),
          name: '数字城建一张图',
          key: 'szcj',
        },
        {
          icon: require('@/assets/home/<USER>'),
          name: '应用集成',
          key: 'yyjc',
        },
      ],
      szcjToken: '',
      canvas: '',
      ctx: '',
      W: '',
      H: '',
      angle: 0,
      mp: 5000,
      particles: [],
    }
  },
  computed: {},
  mounted() {
    // 初始化 POC

    try {
      mainObj.init()

      // 然后初始化视频数据
      this.initPocVideoData()
    } catch (error) {
      console.error('POC 初始化失败:', error)
    }
    clearInterval(this.commInterval)
    this.initOptimizedRotation()
    this._initCavas()
    this.setupEnhancedMouseEvents()
    this.getArea()
    this.getSzcjToken()

    // 设置页面标题
    this.updatePageTitle()
  },
  beforeDestroy() {
    // 清理旋转动画定时器
    clearInterval(this.commInterval)
  },
  methods: {
    // 更新页面标题
    updatePageTitle() {
      const title = this.city + '城市运行管理服务系统'
      document.title = title
    },
    _initCavas() {
      this.canvas = document.getElementById('canvas')
      this.ctx = this.canvas.getContext('2d')
      this.W = window.innerWidth - 30
      this.H = window.innerHeight - 10
      this.canvas.width = this.W
      this.canvas.height = this.H
      //雪花数量
      this.mp = 20
      this.particles = []
      for (var i = 0; i < this.mp; i++) {
        this.particles.push({
          x: Math.random() * this.W, //x-coordinate
          y: Math.random() * this.H, //y-coordinate
          //改变大小
          r: Math.random() * 30 + 10, //radius
          d: Math.random() * this.mp, //density
        })
      }
      setInterval(this.draw, 30)
    },
    draw() {
      this.ctx.clearRect(0, 0, this.W, this.H)
      this.ctx.fillStyle = 'rgba(146,192,227,1)'
      this.ctx.fillStyle = 'border: 1px solid rgb(37, 211, 236,0.2)'
      this.ctx.fillStyle = 'box-shadow: 0px 0px 10px 5px rgba(145,198,239,1)'
      this.ctx.beginPath()
      for (var i = 0; i < this.mp; i++) {
        var p = this.particles[i]
        this.ctx.moveTo(p.x, p.y)
        this.ctx.arc(p.x, p.y, p.r, 0, Math.PI * 2, true)
      }
      this.ctx.fill()
      this.update()
    },
    update() {
      for (var i = 0; i < this.mp; i++) {
        var p = this.particles[i]
        p.y += Math.cos(this.angle + p.d) + 1 + p.r / 2
        p.x += Math.sin(this.angle) * 2

        if (p.x > this.W || p.x < 0 || p.y > this.H) {
          if (i % 3 > 0) {
            this.particles[i] = {
              x: Math.random() * this.W,
              y: -10,
              r: p.r,
              d: p.d,
            }
          } else {
            if (Math.sin(this.angle) > 0) {
              //Enter fromth
              this.particles[i] = {
                x: -5,
                y: -Math.random() * this.H,
                r: p.r,
                d: p.d,
              }
            } else {
              this.particles[i] = {
                x: this.W + 5,
                y: -Math.random() * this.H,
                r: p.r,
                d: p.d,
              }
            }
          }
        }
      }
    },

    /**
     * 优化的圆形旋转函数 - 更大的旋转圈宽度
     */
    funNcl() {
      const menuElements = document.getElementsByClassName('city-swipper-item')
      const elementCount = menuElements.length

      if (elementCount === 0) return

      // 优化的配置参数 - 更大的旋转圈
      const config = {
        containerWidth: 3600,
        containerHeight: 1600,
        radiusX: 1700, // 水平半径增大 (原来是 width/4 = 750，现在是 600)
        radiusY: 400, // 垂直半径增大 (原来是 height/8 = 200，现在是 300)
        speed: -0.008, // 稍微调整速度以适应更大的圈
        centerX: 1800, // containerWidth / 2
        centerY: 800, // containerHeight / 2
      }

      // 计算每个元素之间的角度间隔
      const angleInterval = (2 * Math.PI) / elementCount

      for (let index = 0; index < elementCount; index++) {
        const element = menuElements[index]
        const elementStyle = element.style

        elementStyle.position = 'absolute'

        // 计算当前角度
        const currentAngle = this.i * config.speed + index * angleInterval

        // 使用椭圆轨道公式，创建更大的旋转圈
        const x = Math.sin(currentAngle) * config.radiusX + config.centerX
        const y = Math.cos(currentAngle) * config.radiusY + config.centerY

        // 应用位置
        elementStyle.left = x + 'px'
        elementStyle.top = y + 'px'

        // 添加深度效果 - 根据Y位置调整透明度和缩放
        const depthFactor = (Math.cos(currentAngle) + 1) / 2 // 0 到 1 之间
        const scale = 0.8 + depthFactor * 0.4 // 0.8 到 1.2 之间的缩放
        const opacity = 0.7 + depthFactor * 0.3 // 0.7 到 1.0 之间的透明度

        elementStyle.transform = `scale(${scale})`
        elementStyle.opacity = opacity
        elementStyle.zIndex = Math.floor(depthFactor * 10) + 1
      }

      this.i++
    },
    changeCard(i) {
      console.log(i)
      // 当前选中的左右两侧都显示另一组内容
      if (i == 0 || i == 2) {
        // 当前选中左侧内容，左右两侧都显示右侧菜单
        this.curvedMenuItems = this.rightMenuItems
      } else {
        // 当前选中右侧内容，左右两侧都显示左侧菜单
        this.curvedMenuItems = this.leftMenuItems
      }
    },

    /**
     * 初始化优化的旋转动画
     */
    initOptimizedRotation() {
      // 使用更高的帧率以获得更流畅的动画
      this.commInterval = setInterval(() => {
        this.funNcl()
      }, 60) // 约16.7fps，更流畅
    },

    /**
     * 设置增强的鼠标事件
     */
    setupEnhancedMouseEvents() {
      const self = this

      $('.city-swipper-item').mouseenter(function () {
        // 暂停旋转
        if (self.commInterval) {
          clearInterval(self.commInterval)
          self.commInterval = null
        }

        // 添加悬停效果
        $(this).css({
          transform: 'scale(1.2)',
          'z-index': '100',
          transition: 'all 0.3s ease',
        })
      })

      $('.city-swipper-item').mouseleave(function () {
        // 恢复旋转
        if (!self.commInterval) {
          self.commInterval = setInterval(() => {
            self.funNcl()
          }, 60)
        }

        // 移除悬停效果
        $(this).css({
          transition: 'all 0.3s ease',
        })
      })
    },
    //获取用户所属区县
    getArea() {
      getUserArea().then((res) => {
        if (res.code == 200) {
          localStorage.setItem('city', res.data.area)
          localStorage.setItem('adminCity', res.data.area)
          // 更新 data 中的 city 值，触发页面更新
          this.city = res.data.area
          // 更新页面标题
          this.updatePageTitle()
        }
      })
    },
    async initPocVideoData() {
      try {
        const authInfo = await mainObj.queryAuth()
        console.log('POC 认证信息:', authInfo)

        // 继续处理视频数据...
      } catch (error) {
        console.error('初始化 POC 视频数据失败:', error)
      }
    },
    getSzcjToken() {
      getSzcjToken({
        username: '13735713555',
        password: Encrypt('%&fyAB2%mZ'),
      }).then((res) => {
        console.log('szcjToken', res.token)
        this.szcjToken = res.token
      })
    },
    pageJump(item) {
      switch (item.name) {
        case '绩效评估':
          getUrl('/token/getTokenInfo', { jmppage: 'ks' }).then((res) => {
            if (res.code == 200) {
              this.openHtmlByMode(res.data.url)
            }
          })
          break
        case '三色预警一张图':
          getUrl({ type: 'dashboard', module: 'ssyj' }).then((res) => {
            if (res.code == 200) {
              this.openHtmlByMode(res.data.url)
            }
          })
          break
        case '行政执法综合评价一张图':
          getUrl({ type: 'dashboard', module: 'xzzfpjzb' }).then((res) => {
            if (res.code == 200) {
              this.openHtmlByMode(res.data.url)
            }
          })
          break
        case '首页':
          this.getPage(item)
          break
        case '驾驶舱':
          this.getPage(item)
          break
        case '指挥调度一张图':
          this.getPage(item)
          break
        case '执法态势一张图':
          this.getPage(item)
          break
        case '行政执法一张图':
          this.getPage(item)
          break
        case '城市管理一张图':
          this.getPage(item)
          break
        case '物联网设备一张图':
          this.getPage(item)
          break
        case '公众服务一张图':
          this.getPage(item)
          break
        case '综合评价一张图':
          this.getPage(item)
          break
        case '运行监测一张图':
          this.getPage(item)
          break
        case '犬类管理一张图':
          this.getPage(item)
          break
        case '站前执法一张图':
          this.getPage(item)
          break
        case '县级中心':
          this.getPage(item)
          break
        case '应用集成':
          this.getPage(item)
          break
        case '后台管理':
          this.openHtmlByMode(
            process.env.VUE_APP_BASE_ADMIN_URL + '/home')
          break
        case '案件回访':
          this.getPage(item)
          break
        case '数字城建一张图':
          this.openHtmlByMode('https://jhqy.jsj.jinhua.gov.cn/dplus/view/1672878871176626178?token=' + this.szcjToken)
        case '垃圾分类一张图':
          loginByThirdComplete({
            tenantCode: 'TENANT_JHCC',
            account: 'dddl',
            scope: 'THIRD_APP',
            appKey: '25jhljfl05ygf29',
            appSecret: 'B7FEAAEAD3AD3ED37468A45EF8030E94',
          }).then((res) => {
            if (res.msg == '登录成功') {
              if (res.data.access_token) {
                let access_token = res.data.access_token
                this.openHtmlByMode(`http://ljfl.xzzfj.jinhua.gov.cn/#/autoLogin?token=${access_token}`)
              }
            }
          })
          break
      }
    },
    getPage(item) {
      this.$router.push('/' + item.key)
    },
    openHtmlByMode(url) {
      window.open(url)
    },
    //获取端口号
    getCurrentPortWithDefault() {
      let port = window.location.port
      if (port === '') {
        if (window.location.protocol === 'http:') {
          port = '80'
        } else if (window.location.protocol === 'https:') {
          port = '443'
        }
      }
      return port
    },
  },
  watch: {
    // 监听city变化，更新页面标题
    city() {
      this.$nextTick(() => {
        this.updatePageTitle()
      })
    },
  },
}
</script>

<style lang="less" scoped>
.choosePage {
  width: 100%;
  height: 100%;
  background: url('@/assets/home/<USER>');
  background-size: cover;
  background-position: center;
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
}

.name {
  width: 100%;
  text-align: center;
  font-size: 64px;
  font-family: YouSheBiaoTiHei;
  font-weight: 400;
  color: #e3f3ff;
  margin-top: 70px;
  margin-left: 20px;
  cursor: pointer;
}

.tops {
  // width: 100%;
  height: fit-content;
  box-sizing: border-box;
  display: flex;
  justify-content: right;
  position: absolute;
  top: 75px;
  right: 114px;
}
.topItem {
  width: 420px;
  height: 172px;
  margin-left: 80px;
  cursor: pointer;
  background-size: 100% 100% !important;
}

/* 主要内容容器 */
.main-container {
  flex: 1;
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  flex-direction: column;
  .roud_bkg {
    width: 3387px;
    height: 701px;
    position: absolute;
    top: 771px;
  }
  .car_box {
    width: 2880px;
    height: 1000px;
    margin-top: 200px;
  }
  .wlgz-con {
    width: 3387px;
    height: 701px;
    display: flex;
    justify-content: space-around;
    .city-swipper-item {
      width: 263px;
      height: 317px;
      cursor: pointer;
      transition: all 0.3s ease;

      /* 优化性能 */
      will-change: transform, opacity, left, top;
      backface-visibility: hidden;

      img {
        width: 263px;
        height: 317px;
        border-radius: 8px;
        transition: all 0.3s ease;
      }
    }
  }
}

/* 标准走马灯样式 */
.el-carousel__item--card.is-in-stage {
  z-index: 10;
}
.el-carousel__item--card.is-active {
  z-index: 20;
}
</style>
